[{"name": "route", "host": "**********", "username": "root", "privateKeyPath": "C:\\Users\\<USER>\\.ssh\\yg_ed25519", "uploadOnSave": true, "useTempFile": true, "openSsh": true, "ignore": [".ci", ".git", ".github/**", ".vscode", "rules-list", "**/*.bat", "/*.sh"], "watcher": {"files": "**/*", "autoUpload": false, "autoDelete": false}, "remotePath": "/", "remoteExplorer": {"filesExclude": [".git", ".github", ".vscode", "/bin", "/boot", "/dev", "/lib", "/lib64", "/mnt", "/overlay", "/root", "/proc", "/rom", "/sbin", "/sys", "/usr", "/var", "/www"]}}]