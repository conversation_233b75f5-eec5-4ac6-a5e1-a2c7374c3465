
config redirect
	option name 'ipv4-server-nginx'
	option target 'DNAT'
	option proto 'tcp udp'
	option src 'wan'
	option src_dport '999'
	option dest 'lan'
	option dest_ip '**********'
	option dest_port '80'

config redirect
	option name 'ipv4-server-verysync'
	option target 'DNAT'
	option proto 'tcp udp'
	option src 'wan'
	option src_dport '22330'
	option dest 'lan'
	option dest_ip '**********'
	option dest_port '22330'

config redirect
	option name 'ipv4-server-DHT'
	option target 'DNAT'
	option proto 'udp'
	option src 'wan'
	option src_dport '6771'
	option dest 'lan'
	option dest_ip '**********'
	option dest_port '6771'

config redirect
	option name 'ipv4-server-qbittorrent'
	option target 'DNAT'
	option proto 'tcp udp'
	option src 'wan'
	option src_dport '25399'
	option dest 'lan'
	option dest_ip '**********'
	option dest_port '25399'
	option reflection '0'

config redirect
	option name 'ipv4-room-thunder'
	option target 'DNAT'
	option proto 'udp'
	option src 'wan'
	option src_dport '12345'
	option dest 'lan'
	option dest_port '12345'
	option dest_ip '**********'
	option reflection '0'

config redirect
	option name 'ipv4-room-thunder'
	option target 'DNAT'
	option proto 'tcp'
	option src 'wan'
	option src_dport '54321'
	option dest 'lan'
	option dest_port '54321'
	option dest_ip '**********'
	option reflection '0'

config redirect
	option name 'ipv4-room-thunder'
	option target 'DNAT'
	option proto 'tcp udp'
	option src 'wan'
	option src_dport '15000'
	option dest 'lan'
	option dest_port '15000'
	option dest_ip '**********'
	option reflection '0'

config redirect
	option name 'ipv4-room-bitcomet'
	option target 'DNAT'
	option proto 'tcp udp'
	option src 'wan'
	option src_dport '23398'
	option dest 'lan'
	option dest_ip '**********'
	option dest_port '23398'

config redirect
	option name 'ipv4-room-bitcomet4'
	option target 'DNAT'
	option proto 'tcp udp'
	option src 'wan'
	option src_dport '23399'
	option dest 'lan'
	option dest_ip '**********'
	option dest_port '23399'

config rule
	option name 'Allow-DHCP-Renew'
	option family 'ipv4'
	option target 'ACCEPT'
	option proto 'udp'
	option src 'wan'
	option dest_port '68'

config rule
	option name 'Allow-Ping'
	option family 'ipv4'
	option target 'ACCEPT'
	option proto 'icmp'
	option src 'wan'
	option icmp_type 'echo-request'

config rule
	option name 'Allow-IGMP'
	option family 'ipv4'
	option target 'ACCEPT'
	option proto 'igmp'
	option src 'wan'

config rule
	option name 'Allow-DHCPv6'
	option family 'ipv6'
	option target 'ACCEPT'
	option proto 'udp'
	option src 'wan'
	option src_ip 'fc00::/6'
	option dest_ip 'fc00::/6'
	option dest_port '546'

config rule
	option name 'Allow-MLD'
	option family 'ipv6'
	option target 'ACCEPT'
	option proto 'icmp'
	option src 'wan'
	option src_ip 'fe80::/10'
	list icmp_type '130/0'
	list icmp_type '131/0'
	list icmp_type '132/0'
	list icmp_type '143/0'

config rule
	option name 'Allow-ICMPv6-Input'
	option family 'ipv6'
	option target 'ACCEPT'
	option proto 'icmp'
	option src 'wan'
	option limit '1000/sec'
	list icmp_type 'echo-request'
	list icmp_type 'echo-reply'
	list icmp_type 'destination-unreachable'
	list icmp_type 'packet-too-big'
	list icmp_type 'time-exceeded'
	list icmp_type 'bad-header'
	list icmp_type 'unknown-header-type'
	list icmp_type 'router-solicitation'
	list icmp_type 'neighbour-solicitation'
	list icmp_type 'router-advertisement'
	list icmp_type 'neighbour-advertisement'

config rule
	option name 'Allow-ICMPv6-Forward'
	option family 'ipv6'
	option target 'ACCEPT'
	option proto 'icmp'
	option src 'wan'
	option dest '*'
	option limit '1000/sec'
	list icmp_type 'echo-request'
	list icmp_type 'echo-reply'
	list icmp_type 'destination-unreachable'
	list icmp_type 'packet-too-big'
	list icmp_type 'time-exceeded'
	list icmp_type 'bad-header'
	list icmp_type 'unknown-header-type'

config rule
	option name 'Allow-IPSec-ESP'
	option target 'ACCEPT'
	option proto 'esp'
	option src 'wan'
	option dest 'lan'

config rule
	option name 'Allow-ISAKMP'
	option target 'ACCEPT'
	option proto 'udp'
	option src 'wan'
	option dest 'lan'
	option dest_port '500'

config rule
	option name 'Support-UDP-Traceroute'
	option src 'wan'
	option dest_port '33434:33689'
	option proto 'udp'
	option family 'ipv4'
	option target 'REJECT'
	option enabled '0'

config rule
	option name 'zerotier-input'
	option target 'ACCEPT'
	option src 'wan'
	option proto 'tcp udp'
	option dest_port '9993'

config rule
	option name 'zerotier-forward'
	option target 'ACCEPT'
	option src 'wan'
	option family 'ipv6'
	option dest_port '9993'
	option dest '*'

config rule
	option name 'web'
	option target 'ACCEPT'
	option src 'wan'
	option proto 'tcp'
	option dest_port '888'

config rule
	option name 'frps'
	option target 'ACCEPT'
	option src 'wan'
	option proto 'tcp udp'
	option dest_port '7000'

config rule
	option name 'WireGuard'
	list proto 'udp'
	option src 'wan'
	option dest_port '45678'
	option target 'ACCEPT'

config rule
	option name 'server-dht6'
	option target 'ACCEPT'
	option src 'wan'
	option family 'ipv6'
	option dest '*'
	option proto 'tcp udp'
	option dest_port '6771'
	option dest_ip '::e54:a5ff:fe51:81b0/-64'

config rule
	option name 'server-qbittorrent6'
	option target 'ACCEPT'
	option src 'wan'
	option family 'ipv6'
	option dest '*'
	option proto 'tcp udp'
	option dest_port '25399'
	option dest_ip '::e54:a5ff:fe51:81b0/-64'

config rule
	option name 'room-thunder6'
	option target 'ACCEPT'
	option src 'wan'
	option family 'ipv6'
	option dest '*'
	option proto 'udp'
	option dest_port '12345'
	option dest_ip '::2ef0:5dff:fe26:a43d/-64'

config rule
	option name 'room-thunder6'
	option target 'ACCEPT'
	option src 'wan'
	option family 'ipv6'
	option dest '*'
	option proto 'tcp'
	option dest_port '54321'
	option dest_ip '::2ef0:5dff:fe26:a43d/-64'

config rule
	option name 'room-thunder6'
	option target 'ACCEPT'
	option src 'wan'
	option family 'ipv6'
	option dest '*'
	option proto 'tcp udp'
	option dest_port '15000'
	option dest_ip '::2ef0:5dff:fe26:a43d/-64'

config rule
	option name 'room-bitcomet6'
	option target 'ACCEPT'
	option src 'wan'
	option family 'ipv6'
	option dest '*'
	option proto 'tcp udp'
	option dest_port '23398'
	option dest_ip '::2ef0:5dff:fe26:a43d/-64'

config rule
	option name 'room-bitcomet6'
	option target 'ACCEPT'
	option src 'wan'
	option family 'ipv6'
	option dest '*'
	option proto 'tcp udp'
	option dest_port '23399'
	option dest_ip '::2ef0:5dff:fe26:a43d/-64'

config defaults
	option input 'ACCEPT'
	option output 'ACCEPT'
	option forward 'REJECT'
	option flow_offloading '1'
	option synflood_protect '1'
	option flow_offloading_hw '0'
	option fullcone '2'

config zone
	option name 'lan'
	option input 'ACCEPT'
	option output 'ACCEPT'
	option forward 'ACCEPT'
	list network 'lan'
	list network 'wg0'

config zone
	option name 'wan'
	option input 'REJECT'
	option output 'ACCEPT'
	option forward 'REJECT'
	option masq '1'
	option mtu_fix '1'
	list network 'vwan1'
	list network 'vwan2'
	list network 'vwan3'
	list network 'wan'

config forwarding
	option src 'lan'
	option dest 'wan'

config include
	option path '/etc/firewall.user'

config include 'mia'
	option type 'script'
	option path '/etc/mia.include'
	option reload '1'

config include 'openclash'
	option type 'script'
	option path '/var/etc/openclash.include'
	option reload '1'
