
config config_subscribe
	option name 'config'
	option udp 'true'
	option sort 'false'
	option rule_provider 'false'
	option sub_convert '1'
	option node_type 'false'
	option emoji 'false'
	option template '0'
	option custom_template_url 'https://92mg.com/clash_adg_default.yaml'
	option convert_address 'http://localhost:25500/sub'
	option enabled '1'
	option sub_ua 'openclash'
	option skip_cert_verify 'true'
	option address 'https://7mev3.no-mad-world.club/link/Niv4ng6qlD4Cf34N?clash=3&extend=1
hysteria2://orrwMWaXVnQfH5QVWOCUxGTw00U@**********:10099?sni=bing.com&insecure=1#hysteria2
https://92mg.com:888/clash/PaoluzX.yaml'

config config_subscribe
	option name 'ikuuu'
	option udp 'true'
	option sort 'false'
	option rule_provider 'false'
	option sub_convert '1'
	option node_type 'false'
	option emoji 'false'
	option convert_address 'http://localhost:25500/sub'
	option enabled '0'
	option sub_ua 'openclash'
	option skip_cert_verify 'true'
	option address 'https://7mev3.no-mad-world.club/link/Niv4ng6qlD4Cf34N?clash=3&extend=1'
	option template '默认（附带用于Clash的AdGuard DNS）'

config config_subscribe
	option name 'aliyun'
	option udp 'true'
	option sort 'false'
	option rule_provider 'false'
	option sub_convert '1'
	option node_type 'false'
	option emoji 'false'
	option template '0'
	option custom_template_url 'https://92mg.com/clash_adg_default.yaml'
	option convert_address 'http://localhost:25500/sub'
	option enabled '1'
	option sub_ua 'openclash'
	option skip_cert_verify 'true'
	option address 'hysteria2://ijYyCQSEmsmnkoi1Bz9i97ExI4@*************:10088?sni=bing.com&insecure=1#日本阿里1
hysteria2://ijYyCQSEmsmnkoi1Bz9i97ExI4@***********:10088?sni=bing.com&insecure=1#日本阿里2'

config dns_servers
	option group 'nameserver'
	option type 'udp'
	option ip '************'
	option enabled '1'

config dns_servers
	option enabled '1'
	option group 'nameserver'
	option type 'udp'
	option ip '*********'

config dns_servers
	option group 'nameserver'
	option type 'udp'
	option ip '************'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip 'dns.google'
	option type 'tls'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip 'dns.google/dns-query'
	option type 'https'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip 'dns64.dns.google/dns-query'
	option type 'https'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '*******'
	option type 'tls'
	option enabled '1'

config dns_servers
	option group 'fallback'
	option ip '*******/dns-query'
	option type 'https'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '2001:4860:4860::64'
	option type 'tls'
	option port '853'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '2001:4860:4860::6464'
	option type 'tls'
	option port '853'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '2001:4860:4860::8844'
	option port '853'
	option type 'tls'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '2001:4860:4860::8888'
	option port '853'
	option type 'tls'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip 'one.one.one.one'
	option type 'tls'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '*******'
	option type 'tls'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '*******'
	option type 'tls'
	option enabled '1'

config dns_servers
	option group 'fallback'
	option ip 'dns.cloudflare.com'
	option type 'tls'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '**************'
	option type 'tls'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '**************'
	option type 'tls'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '2606:4700::6810:f8f9'
	option type 'tls'
	option port '853'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip '2606:4700::6810:f9f9'
	option type 'tls'
	option port '853'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option enabled '0'
	option ip 'public.dns.iij.jp'
	option type 'tls'

config dns_servers
	option group 'fallback'
	option enabled '0'
	option ip '**********'
	option type 'tls'

config dns_servers
	option group 'fallback'
	option enabled '0'
	option ip '**********'
	option type 'tls'

config dns_servers
	option group 'fallback'
	option enabled '0'
	option ip '2001:300::6'
	option type 'tls'
	option port '853'

config dns_servers
	option group 'fallback'
	option enabled '0'
	option ip '2001:300::5'
	option type 'tls'
	option port '853'

config dns_servers
	option group 'fallback'
	option type 'https'
	option ip 'jp.tiar.app/dns-query'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option type 'https'
	option ip 'jp.tiarap.org/dns-query'
	option enabled '0'

config dns_servers
	option group 'fallback'
	option ip 'jp.tiar.app'
	option type 'tls'
	option enabled '0'

config openclash 'config'
	option proxy_port '7892'
	option tproxy_port '7895'
	option mixed_port '7893'
	option socks_port '7891'
	option http_port '7890'
	option dns_port '7874'
	option update '0'
	option cn_port '9090'
	option servers_if_update '0'
	option proxy_mode 'rule'
	option interface_name '0'
	option log_size '1024'
	option store_fakeip '1'
	option bypass_gateway_compatible '0'
	option release_branch 'master'
	option chnr6_custom_url 'https://ispip.clang.cn/all_cn_ipv6.txt'
	option core_version 'linux-amd64'
	option restricted_mode '0'
	option auto_restart '0'
	option auto_restart_week_time '1'
	option auto_restart_day_time '0'
	option chnr_auto_update '1'
	option small_flash_memory '0'
	option enable_meta_core '1'
	option enable_tcp_concurrent '1'
	option enable_geoip_dat '1'
	option core_type 'Meta'
	option dler_email 'root'
	option dler_passwd '3t-XKa5c6F-d:wh'
	option append_wan_dns '0'
	option enable_custom_dns '1'
	option dashboard_forward_ssl '0'
	option urltest_address_mod '0'
	option create_config '0'
	option rule_source '0'
	option other_rule_auto_update '0'
	option client_fingerprint '0'
	option custom_fallback_filter '1'
	list lan_ac_black_ports '6771'
	list lan_ac_black_ports '12345'
	option servers_update '0'
	option chnr_update_day_time '4'
	option append_default_dns '0'
	option dns_advanced_setting '0'
	option enable_redirect_dns '1'
	option disable_masq_cache '1'
	option disable_udp_quic '1'
	option enable_custom_domain_dns_server '0'
	option geo_update_week_time '1'
	option geo_update_day_time '0'
	option urltest_interval_mod '0'
	option custom_name_policy '0'
	option custom_host '0'
	option chnr_update_week_time '4'
	option enable_custom_clash_rules '0'
	option enable_rule_proxy '0'
	option default_resolvfile '/tmp/resolv.conf.d/resolv.conf.auto'
	option dnsmasq_resolvfile '/tmp/resolv.conf.d/resolv.conf.auto'
	option dashboard_forward_domain '92mg.com'
	option proxy_dns_group 'Disable'
	option skip_proxy_address '1'
	option chnr_custom_url 'https://ispip.clang.cn/all_cn.txt'
	option find_process_mode '0'
	option global_client_fingerprint '0'
	option tolerance '0'
	option log_level 'info'
	option geodata_loader '0'
	option enable_meta_sniffer '1'
	option enable_meta_sniffer_pure_ip '1'
	option enable_meta_sniffer_custom '0'
	option enable_unified_delay '1'
	option keep_alive_interval '3600'
	option delay_start '10'
	option ipv6_enable '0'
	option ipv6_dns '0'
	option filter_aaaa_dns '0'
	option custom_china_domain_dns_server '***************'
	option operation_mode 'redir-host'
	option en_mode 'redir-host'
	option enable_udp_proxy '0'
	option lan_ac_mode '0'
	option common_ports '21 22 23 53 80 123 143 194 443 465 587 853 993 995 998 2052 2053 2082 2083 2086 2095 2096 5222 5228 5229 5230 8080 8443 8880 8888 8889'
	option china_ip_route '0'
	option dashboard_password 'WHjpdUn2'
	option geo_custom_url 'https://testingcf.jsdelivr.net/gh/alecthw/mmdb_china_ip_list@release/lite/Country.mmdb'
	option enable '1'
	option intranet_allowed '0'
	option lan_interface_name 'eth0'
	list lan_ac_black_macs '18:c0:4d:39:c9:29'
	option cndomain_custom_url 'https://wget.la/https://raw.githubusercontent.com/felixonmars/dnsmasq-china-list/master/accelerated-domains.china.conf'
	option github_address_mod 'https://testingcf.jsdelivr.net/'
	option router_self_proxy '1'
	option stream_domains_prefetch '0'
	option stream_auto_select '0'
	option config_reload '1'
	option dashboard_type 'Meta'
	option yacd_type 'Meta'
	option dnsmasq_noresolv '0'
	option restart '0'
	option disable_quic_go_gso '0'
	option geo_auto_update '0'
	option geoip_auto_update '0'
	option geosite_auto_update '0'
	option config_path '/etc/openclash/config/config.yaml'
	option auto_update '0'
	option redirect_dns '1'
	option dnsmasq_cachesize '8192'
	option cachesize_dns '1'

config authentication
	option enabled '1'
	option username 'Clash'
	option password 'Zsrg2y8H'
