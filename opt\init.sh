#!/bin/bash

/etc/init.d/uhttpd stop >/dev/null 2>&1
/etc/init.d/uhttpd disable
/etc/init.d/nginx start

if [ -f "/opt/subconverter/init.d/subconverter" ]; then
  ln -sf /opt/subconverter/init.d/subconverter /etc/init.d/subconverter
  /etc/init.d/subconverter enable
  /etc/init.d/subconverter start
fi

if [ -f "/opt/dandanproxy/init.d/dandanproxy" ]; then
  ln -sf /opt/dandanproxy/init.d/dandanproxy /etc/init.d/dandanproxy
  /etc/init.d/dandanproxy enable
  /etc/init.d/dandanproxy start
fi

opkg update
opkg install ca-bundle

# >>>>>>>>>>>>>>>>>>>>>>>> openclash >>>>>>>>>>>>>>>>>>>>>>>>
[ ! -d /opt/openclash ] && mkdir /opt/openclash
if [ ! -h "/usr/share/openclash/ui" ]; then
  if [ -d "/usr/share/openclash/ui" ]; then
    cp -rf "/usr/share/openclash/ui" "/opt/openclash/"
    rm -rf "/usr/share/openclash/ui"
  elif [ ! -d "/opt/openclash/ui" ]; then
    mkdir "/opt/openclash/ui"
  fi
  ln -sf "/opt/openclash/ui" "/usr/share/openclash/ui"
fi

/opt/openclash_file_check.sh

uci set openclash.config.enable=1 2>/dev/null
uci delete network.utun 2>/dev/null
uci commit 2>/dev/null
# <<<<<<<<<<<<<<<<<<<<<<<< openclash <<<<<<<<<<<<<<<<<<<<<<<<

# >>>>>>>>>>>>>>>>>>>>>>>> oh-my-zsh >>>>>>>>>>>>>>>>>>>>>>>>
ln -sf /opt/.zshrc /root/.zshrc
ln -sf /opt/.oh-my-zsh /root/.oh-my-zsh
# <<<<<<<<<<<<<<<<<<<<<<<< oh-my-zsh <<<<<<<<<<<<<<<<<<<<<<<<

/opt/setup.sh
