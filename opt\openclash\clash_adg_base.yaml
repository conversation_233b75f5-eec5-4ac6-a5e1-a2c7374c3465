mixed-port: {{ default(global.clash.http_port, "7890") }}
allow-lan: {{ default(global.clash.allow_lan, "true") }}
mode: rule
log-level: {{ default(global.clash.log_level, "info") }}
external-controller: :9090
find-process-mode: always

{% if default(request.adg, "true") == "true" or exists("request.adg.port") %}
dns:
  enable: true
  listen: 127.0.0.1:{{ default(request.adg.port, "5450") }}
  ipv6: false
  use-hosts: true
  use-system-hosts: true
  default-nameserver:
    - system
    - *********
    - ************
  nameserver:
    - https://doh.pub/dns-query
    - https://dns.alidns.com/dns-query
  proxy-server-nameserver:
    - https://doh.pub/dns-query
  fallback:
    - *******#🚀代理
    - *******#🚀代理
  fallback-filter:
    geoip: true
    geoip-code: CN
    domain:
    - "+.google.com"
    - "+.facebook.com"
    - "+.youtube.com"
    - "+.githubusercontent.com"
    - "+.googlevideo.com"
    - "+.msftconnecttest.com"
    - "+.msftncsi.com"
    ipcidr:
    - ::/128
    - ::1/128
    - 2001::/32
    - 0.0.0.0/8
    - 10.0.0.0/8
    - **********/10
    - *********/8
    - ***********/16
    - **********/12
    - *********/24
    - *********/24
    - ***********/24
    - ***********/16
    - **********/15
    - ************/24
    - ***********/24
    - *********/4
    - 240.0.0.0/4
    - ***************/32
{% endif %}

geodata-mode: true
geox-url:
  geoip: "https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geoip-lite.dat"
  geosite: "https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/geosite.dat"
  mmdb: "https://testingcf.jsdelivr.net/gh/MetaCubeX/meta-rules-dat@release/country.mmdb"
  asn: "https://testingcf.jsdelivr.net/gh/xishang0128/geoip@releases/GeoLite2-ASN.mmdb"

profile:
  store-selected: true
  store-fake-ip: true

sniffer:
  enable: true
  override-destination: true
  sniff:
    QUIC:
      ports:
      - 443
    TLS:
      ports:
      - 443
      - 8443
    HTTP:
      ports:
      - 80
      - 8080-8880
      override-destination: true
  force-domain:
  - "+.netflix.com"
  - "+.nflxvideo.net"
  - "+.amazonaws.com"
  - "+.media.dssott.com"
  skip-domain:
  - "+.apple.com"
  - Mijia Cloud
  - dlg.io.mi.com
  - "+.oray.com"
  - "+.sunlogin.net"
  - "+.push.apple.com"
  force-dns-mapping: true
  parse-pure-ip: true

{% if local.clash.new_field_name == "true" %}
proxies: ~
proxy-groups: ~
rules: ~
{% else %}
Proxy: ~
Proxy Group: ~
Rule: ~
{% endif %}
