#!/bin/bash

for item in {backup,config,core,custom,game_rules,history,proxy_provider,rule_provider}; do
  if [ ! -h "/etc/openclash/${item}" ]; then
    if [ -d "/etc/openclash/${item}" ]; then
      cp -rf "/etc/openclash/${item}" "/opt/openclash/"
      rm -rf "/etc/openclash/${item}"
    elif [ ! -d "/opt/openclash/${item}" ]; then
      mkdir "/opt/openclash/${item}"
    fi
    ln -sf "/opt/openclash/${item}" "/etc/openclash/${item}"
  fi
done

for item in {Country.mmdb,GeoIP.dat,GeoSite.dat,accelerated-domains.china.conf,china_ip_route.ipset,china_ip6_route.ipset}; do
  if [ ! -h "/etc/openclash/${item}" ]; then
    if [ -f "/etc/openclash/${item}" ]; then
      echo """${item}"" if file!!!"
      mv "/etc/openclash/${item}" "/opt/openclash/"
    fi
    [ -f "/opt/openclash/${item}" ] && ln -sf "/opt/openclash/${item}" "/etc/openclash/${item}"
  fi
done
