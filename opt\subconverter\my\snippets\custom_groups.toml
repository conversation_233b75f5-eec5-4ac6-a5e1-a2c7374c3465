[[custom_groups]]
name = '🚀代理'
type = 'select'
rule = [
    '[]♻️自动',
    '[]🔀均衡',
    '.*',
]

# [[custom_groups]]
# name = '💰交易'
# type = 'select'
# rule = [
#     '[]🔀均衡',
#     '[]🚀代理',
#     '.*',
# ]

[[custom_groups]]
name = 'Ⓜ️微软'
type = 'select'
rule = [
    '[]🎯直连',
    '[]🚀代理',
]

[[custom_groups]]
name = '📱电报'
type = 'select'
rule = [
    '[]🔀均衡',
    '[]🚀代理',
]

[[custom_groups]]
name = '⬇️下载'
type = 'select'
rule = [
    '[]🔀均衡',
    '[]🚀代理',
    '[]🎯直连',
]

[[custom_groups]]
name = '📺巴哈'
type = 'select'
rule = [
    '[]🚀代理',
    '[]🎯直连',
    '.*',
]

[[custom_groups]]
name = '🎞️Tiktok'
type = 'select'
rule = [
    '[]🚀代理',
    '[]🇭🇰香港',
    '[]🇹🇼台湾',
    '[]🇯🇵日本',
    '[]🇺🇸美国',
    '[]🇸🇬新加坡',
    '[]🇲🇾马来西亚',
]

[[custom_groups]]
name = '🐟漏网'
type = 'select'
icon = 'https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/fish.svg'
rule = [
    '[]🎯直连',
    '[]🚀代理',
]

[[custom_groups]]
name = '⛔️广告'
type = 'select'
hidden = true
rule = [
    '[]REJECT',
    '[]PASS',
]
[[custom_groups]]
name = '🎯直连'
type = 'select'
hidden = true
rule = [
    '[]DIRECT'
]

[[custom_groups]]
name = '♻️自动'
type = 'url-test'
rule = [
    '(^(?!.*(自建|倍|量|x)).*)',
]
url = 'http://www.gstatic.com/generate_204'
interval = 300

[[custom_groups]]
name = '🔀均衡'
type = 'load-balance'
rule = [
    # '(^(?!.*(hysteria2|免费|自建|日边缘|美|英|韩|马||马|加|印|土|阿|德|倍|量|号|x)).*)',
    '.*(🇭🇰|🇹🇼|🇸🇬|日本).*',
]
url = 'http://www.gstatic.com/generate_204'
strategy = 'round-robin'
interval = 100


[[custom_groups]]
name = '🇭🇰香港'
type = 'url-test'
rule = [
    '.*(🇭🇰).*',
]
url = 'http://www.gstatic.com/generate_204'
interval = 300

[[custom_groups]]
name = '🇹🇼台湾'
type = 'url-test'
rule = [
    '.*(🇹🇼).*',
]
url = 'http://www.gstatic.com/generate_204'
interval = 300

[[custom_groups]]
name = '🇯🇵日本'
type = 'url-test'
rule = [
    '.*(🇯🇵).*',
]
url = 'http://www.gstatic.com/generate_204'
interval = 300

[[custom_groups]]
name = '🇺🇸美国'
type = 'url-test'
rule = [
    '.*(🇺🇸).*',
]
url = 'http://www.gstatic.com/generate_204'
interval = 300


[[custom_groups]]
name = '🇸🇬新加坡'
type = 'url-test'
rule = [
    '.*(🇸🇬).*',
]
url = 'http://www.gstatic.com/generate_204'
interval = 300

[[custom_groups]]
name = '🇲🇾马来西亚'
type = 'url-test'
rule = [
    '.*(🇲🇾).*',
]
url = 'http://www.gstatic.com/generate_204'
interval = 300
