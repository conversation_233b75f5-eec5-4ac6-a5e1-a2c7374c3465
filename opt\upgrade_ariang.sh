#!/bin/bash

# gitproxy="https://githubfast.com"
gitproxy="https://gh-proxy.com/https://github.com"

echo "========== upgrade AriaNg …  =========="
name="AriaNg"
repos="mayswind/AriaNg"
dstdir="/opt/ariang"
local=$(cat ${dstdir}/version.txt)
echo "local version: ${local}"

latest=$(curl -fsSL "https://api.github.com/repos/${repos}/releases?per_page=1" | jq .[0])
tab_name=$(echo "${latest}" | jq -r .tag_name)
version=${tab_name}
echo "latest version: ${version}"

if [[ "${version}" != "${local}" ]]; then
    curl -fSL "${gitproxy}/${repos}/releases/download/${tab_name}/AriaNg-${version}.zip" -o "/tmp/${name}.zip"
    rm -rf "${dstdir}"
    unzip "/tmp/${name}.zip" -d "${dstdir}"
    rm "/tmp/${name}.zip"
    echo "${version}" >"${dstdir}/version.txt"
fi
echo -e "========= upgrade AriaNg over =========\n"
